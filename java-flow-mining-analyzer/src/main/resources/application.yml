server:
  port: 8081
  servlet:
    context-path: /
    multipart:
      max-file-size: 50MB
      max-request-size: 50MB

spring:
  application:
    name: flow-mining-analyzer
  
  servlet:
    multipart:
      enabled: true
      max-file-size: 50MB
      max-request-size: 50MB
      file-size-threshold: 2KB
      location: ${java.io.tmpdir}
  
  jackson:
    default-property-inclusion: non_null
    serialization:
      write-dates-as-timestamps: false
    deserialization:
      fail-on-unknown-properties: false

# AI API配置
ai:
  api:
    key: ${AI_API_KEY:sk-eddb8e0cf8c14e48a8dc4018a988591e}
    base-url: ${AI_BASE_URL:https://dashscope.aliyuncs.com/compatible-mode/v1}
  model:
    name: ${AI_MODEL_NAME:qwen-turbo}
    max-tokens: ${AI_MAX_TOKENS:2000}
    temperature: ${AI_TEMPERATURE:0.7}
  rate-limit:
    interval: ${AI_RATE_LIMIT_INTERVAL:1000}
  timeout:
    seconds: ${AI_TIMEOUT_SECONDS:120}

app:
  upload:
    directory: ${APP_UPLOAD_DIR:uploads}
  output:
    directory: ${APP_OUTPUT_DIR:outputs}
  temp:
    directory: ${APP_TEMP_DIR:temp}
  domain:
    default: ${APP_DEFAULT_DOMAIN:通用}
  cleanup:
    enabled: ${APP_CLEANUP_ENABLED:true}
    interval-hours: ${APP_CLEANUP_INTERVAL:24}
    max-age-hours: ${APP_MAX_AGE:72}

logging:
  level:
    com.flowmining: INFO
    org.springframework.web: INFO
    org.apache.poi: WARN
    org.springframework.web.reactive.function.client: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/flow-mining-analyzer.log
    max-size: 10MB
    max-history: 30

management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: when-authorized

# 异步任务配置
async:
  core-pool-size: 2
  max-pool-size: 10
  queue-capacity: 100
  thread-name-prefix: "async-"
