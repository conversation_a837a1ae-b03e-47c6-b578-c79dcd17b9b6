package com.flowmining.controller;

import com.flowmining.model.ProcessingRequest;
import com.flowmining.model.TaskInfo;
import com.flowmining.model.TaskStatus;
import com.flowmining.service.JsonLoaderService;
import com.flowmining.service.ReportProcessorService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.FileSystemResource;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import java.io.File;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

@RestController
@RequestMapping("/api/process-mining")
@CrossOrigin(origins = "*")
public class ProcessMiningController {
    
    private static final Logger logger = LoggerFactory.getLogger(ProcessMiningController.class);
    
    @Autowired
    private ReportProcessorService reportProcessorService;
    
    @Autowired
    private JsonLoaderService jsonLoaderService;
    
    @Value("${app.upload.directory:uploads}")
    private String uploadDirectory;
    
    @Value("${app.output.directory:outputs}")
    private String outputDirectory;

    @GetMapping("/health")
    public ResponseEntity<Map<String, String>> health() {
        Map<String, String> response = new HashMap<>();
        response.put("status", "OK");
        response.put("timestamp", LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));
        response.put("service", "Flow Mining Analyzer");
        return ResponseEntity.ok(response);
    }

    @PostMapping("/upload")
    public ResponseEntity<Map<String, Object>> uploadFile(
            @RequestParam("file") MultipartFile file,
            @RequestParam(value = "domain", required = false) String domain) {
        
        Map<String, Object> response = new HashMap<>();
        
        try {
            // 基本验证
            if (file.isEmpty()) {
                response.put("success", false);
                response.put("message", "文件为空");
                return ResponseEntity.badRequest().body(response);
            }
            
            if (!file.getOriginalFilename().toLowerCase().endsWith(".json")) {
                response.put("success", false);
                response.put("message", "仅支持JSON文件");
                return ResponseEntity.badRequest().body(response);
            }
            
            // 生成任务ID
            String taskId = UUID.randomUUID().toString();
            
            // 确保上传目录存在
            new File(uploadDirectory).mkdirs();
            
            // 保存上传的文件
            String uploadPath = new File(uploadDirectory, taskId + ".json").getAbsolutePath();
            file.transferTo(new File(uploadPath));
            
            // 加载JSON数据
            Object jsonData = jsonLoaderService.fromFile(uploadPath);
            
            // 创建任务
            TaskInfo task = reportProcessorService.createTask(taskId);
            
            // 异步处理报告生成
            reportProcessorService.processReportAsync(taskId, jsonData, domain, null);
            
            response.put("success", true);
            response.put("taskId", taskId);
            response.put("message", "文件上传成功，分析已开始");
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("文件上传失败: {}", e.getMessage(), e);
            response.put("success", false);
            response.put("message", "上传失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    @PostMapping("/process-json")
    public ResponseEntity<Map<String, Object>> processJson(@Valid @RequestBody ProcessingRequest request) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            logger.info("接收到处理请求: {}", request);
            
            // 生成任务ID
            String taskId = UUID.randomUUID().toString();
            
            // 确保输出目录存在
            new File(outputDirectory).mkdirs();
            
            // 保存JSON数据到文件（可选，用于调试）
            new File(uploadDirectory).mkdirs();
            String jsonPath = new File(uploadDirectory, taskId + ".json").getAbsolutePath();
            jsonLoaderService.saveToFile(request.getJsonData(), jsonPath);
            
            // 创建任务
            TaskInfo task = reportProcessorService.createTask(taskId);
            
            // 异步处理报告生成
            reportProcessorService.processReportAsync(
                taskId, 
                request.getJsonData(), 
                request.getDomain(), 
                request.getEffectivePrompt()
            );
            
            response.put("success", true);
            response.put("task_id", taskId);
            response.put("status", task.getStatus().getValue());
            response.put("message", "数据已接收，正在处理中");
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("处理JSON数据失败: {}", e.getMessage(), e);
            response.put("success", false);
            response.put("message", "处理失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    @GetMapping("/status/{taskId}")
    public ResponseEntity<TaskInfo> getTaskStatus(@PathVariable String taskId) {
        try {
            TaskInfo task = reportProcessorService.getTaskInfo(taskId);
            
            if (task == null) {
                return ResponseEntity.notFound().build();
            }
            
            return ResponseEntity.ok(task);
            
        } catch (Exception e) {
            logger.error("查询任务状态失败: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @GetMapping("/download/{taskId}")
    public ResponseEntity<Resource> downloadReport(@PathVariable String taskId) {
        try {
            TaskInfo task = reportProcessorService.getTaskInfo(taskId);
            
            if (task == null) {
                return ResponseEntity.notFound().build();
            }
            
            if (task.getStatus() != TaskStatus.COMPLETED) {
                return ResponseEntity.badRequest().build();
            }
            
            if (task.getOutputFile() == null) {
                return ResponseEntity.notFound().build();
            }
            
            File file = new File(outputDirectory, task.getOutputFile());
            if (!file.exists()) {
                return ResponseEntity.notFound().build();
            }
            
            Resource resource = new FileSystemResource(file);
            String filename = String.format("流程挖掘分析报告_%s.docx", 
                LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd")));
            
            return ResponseEntity.ok()
                    .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + filename + "\"")
                    .contentType(MediaType.parseMediaType("application/vnd.openxmlformats-officedocument.wordprocessingml.document"))
                    .body(resource);
                    
        } catch (Exception e) {
            logger.error("下载报告失败: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @GetMapping("/tasks")
    public ResponseEntity<Map<String, Object>> listTasks(
            @RequestParam(value = "status", required = false) String status,
            @RequestParam(value = "limit", defaultValue = "50") int limit,
            @RequestParam(value = "offset", defaultValue = "0") int offset) {
        
        try {
            List<TaskInfo> tasks;
            
            if (status != null && !status.trim().isEmpty()) {
                TaskStatus taskStatus = TaskStatus.fromString(status);
                tasks = reportProcessorService.getTasksByStatus(taskStatus);
            } else {
                tasks = reportProcessorService.getAllTasks();
            }
            
            // 分页
            int total = tasks.size();
            int endIndex = Math.min(offset + limit, total);
            List<TaskInfo> pagedTasks = tasks.subList(offset, endIndex);
            
            Map<String, Object> response = new HashMap<>();
            response.put("total", total);
            response.put("limit", limit);
            response.put("offset", offset);
            response.put("tasks", pagedTasks);
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("获取任务列表失败: {}", e.getMessage(), e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("error", "获取任务列表失败: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
        }
    }

    @DeleteMapping("/task/{taskId}")
    public ResponseEntity<Map<String, String>> deleteTask(@PathVariable String taskId) {
        try {
            TaskInfo task = reportProcessorService.getTaskInfo(taskId);
            
            if (task == null) {
                return ResponseEntity.notFound().build();
            }
            
            // 删除相关文件
            try {
                // 删除上传的JSON文件
                File uploadFile = new File(uploadDirectory, taskId + ".json");
                if (uploadFile.exists()) {
                    uploadFile.delete();
                }
                
                // 删除生成的报告文件
                if (task.getOutputFile() != null) {
                    File outputFile = new File(outputDirectory, task.getOutputFile());
                    if (outputFile.exists()) {
                        outputFile.delete();
                    }
                }
            } catch (Exception e) {
                logger.warn("删除任务文件时出错: {}", e.getMessage());
            }
            
            // 删除任务记录
            reportProcessorService.deleteTask(taskId);
            
            Map<String, String> response = new HashMap<>();
            response.put("message", "任务已删除");
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("删除任务失败: {}", e.getMessage(), e);
            Map<String, String> errorResponse = new HashMap<>();
            errorResponse.put("error", "删除失败: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
        }
    }
    
    @GetMapping("/ai-config")
    public ResponseEntity<Map<String, Object>> getAIConfig() {
        try {
            // 这里可以返回AI配置信息（不包含敏感信息如API Key）
            Map<String, Object> config = new HashMap<>();
            config.put("model", "qwen-turbo");
            config.put("max_tokens", 2000);
            config.put("temperature", 0.7);
            config.put("domain", "通用");
            
            return ResponseEntity.ok(config);
            
        } catch (Exception e) {
            logger.error("获取AI配置失败: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
    
    @PostMapping("/ai-config")
    public ResponseEntity<Map<String, String>> updateAIConfig(@RequestBody Map<String, Object> config) {
        try {
            // 这里可以实现AI配置更新逻辑
            // 注意：实际部署时需要考虑安全性
            
            Map<String, String> response = new HashMap<>();
            response.put("message", "配置更新功能将在后续版本中实现");
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("更新AI配置失败: {}", e.getMessage(), e);
            Map<String, String> errorResponse = new HashMap<>();
            errorResponse.put("error", "更新失败: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
        }
    }
}
