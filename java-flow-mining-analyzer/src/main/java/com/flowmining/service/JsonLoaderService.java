package com.flowmining.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 * JSON数据加载服务
 */
@Service
public class JsonLoaderService {
    
    private static final Logger logger = LoggerFactory.getLogger(JsonLoaderService.class);
    
    private final ObjectMapper objectMapper;
    
    public JsonLoaderService() {
        this.objectMapper = new ObjectMapper();
    }
    
    /**
     * 从文件加载JSON数据
     */
    public JsonNode fromFile(String filePath) throws IOException {
        try {
            File file = new File(filePath);
            if (!file.exists()) {
                throw new IllegalArgumentException("JSON文件不存在: " + filePath);
            }
            
            JsonNode data = objectMapper.readTree(file);
            logger.info("成功从{}加载JSON数据", filePath);
            return data;
        } catch (IOException e) {
            logger.error("加载JSON文件时发生错误: {}", e.getMessage());
            throw new IOException("加载JSON文件时发生错误: " + e.getMessage(), e);
        }
    }
    
    /**
     * 从字符串加载JSON数据
     */
    public JsonNode fromString(String jsonString) throws IOException {
        try {
            JsonNode data = objectMapper.readTree(jsonString);
            logger.info("成功从字符串加载JSON数据");
            return data;
        } catch (IOException e) {
            logger.error("JSON字符串格式错误: {}", e.getMessage());
            throw new IOException("JSON字符串格式错误: " + e.getMessage(), e);
        }
    }
    
    /**
     * 从对象加载JSON数据
     */
    public JsonNode fromObject(Object data) {
        try {
            JsonNode jsonNode = objectMapper.valueToTree(data);
            logger.info("成功从对象加载JSON数据");
            return jsonNode;
        } catch (Exception e) {
            logger.error("对象转换为JSON时发生错误: {}", e.getMessage());
            throw new RuntimeException("对象转换为JSON时发生错误: " + e.getMessage(), e);
        }
    }
    
    /**
     * 将数据标准化为列表格式
     */
    public List<JsonNode> normalizeData(JsonNode data) {
        List<JsonNode> result = new ArrayList<>();
        
        if (data.isArray()) {
            data.forEach(result::add);
        } else if (data.isObject()) {
            result.add(data);
        } else {
            logger.error("不支持的数据类型: {}", data.getNodeType());
            throw new IllegalArgumentException("不支持的数据类型: " + data.getNodeType());
        }
        
        return result;
    }
    
    /**
     * 保存JSON数据到文件
     */
    public void saveToFile(Object data, String filePath) throws IOException {
        try {
            objectMapper.writerWithDefaultPrettyPrinter().writeValue(new File(filePath), data);
            logger.info("成功保存JSON数据到: {}", filePath);
        } catch (IOException e) {
            logger.error("保存JSON文件时发生错误: {}", e.getMessage());
            throw new IOException("保存JSON文件时发生错误: " + e.getMessage(), e);
        }
    }
}