package com.flowmining.service;

import com.fasterxml.jackson.databind.JsonNode;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * JSON数据验证服务
 */
@Service
public class JsonValidatorService {
    
    private static final Logger logger = LoggerFactory.getLogger(JsonValidatorService.class);
    
    /**
     * 验证单个模块的结构
     */
    public ValidationResult validateModule(JsonNode module) {
        List<String> errors = new ArrayList<>();
        
        // 检查必填字段 title
        if (!module.has("title") || module.get("title").asText().trim().isEmpty()) {
            errors.add("模块缺少必填字段 'title'");
        }
        
        // 检查简单模块的kpiValue字段
        if (module.has("simple") && module.get("simple").asBoolean()) {
            if (!module.has("kpiValue")) {
                errors.add("简单模块缺少必填字段 'kpiValue'");
            }
        }
        
        // 检查数据结构
        if (module.has("data")) {
            JsonNode data = module.get("data");
            if (data.isArray() && data.size() > 0) {
                // 验证数据数组结构
                validateDataArray(data, errors);
            }
        }
        
        // 检查元数据结构
        if (module.has("metadata")) {
            validateMetadata(module.get("metadata"), errors);
        }
        
        return new ValidationResult(errors.isEmpty(), errors);
    }
    
    /**
     * 批量验证所有模块
     */
    public ValidationResult validateAllModules(List<JsonNode> modules) {
        List<String> allErrors = new ArrayList<>();
        
        for (int i = 0; i < modules.size(); i++) {
            ValidationResult result = validateModule(modules.get(i));
            if (!result.isValid()) {
                for (String error : result.getErrors()) {
                    allErrors.add(String.format("模块 %d: %s", i + 1, error));
                }
            }
        }
        
        if (!allErrors.isEmpty()) {
            logger.warn("数据验证发现 {} 个问题", allErrors.size());
        } else {
            logger.info("所有模块验证通过");
        }
        
        return new ValidationResult(allErrors.isEmpty(), allErrors);
    }
    
    /**
     * 验证数据数组结构
     */
    private void validateDataArray(JsonNode dataArray, List<String> errors) {
        JsonNode firstRow = dataArray.get(0);
        if (!firstRow.isObject()) {
            errors.add("数据数组第一行应该是对象类型");
            return;
        }
        
        // 检查数据一致性
        int expectedFields = firstRow.size();
        for (int i = 1; i < dataArray.size(); i++) {
            JsonNode row = dataArray.get(i);
            if (!row.isObject()) {
                errors.add(String.format("数据数组第 %d 行不是对象类型", i + 1));
                continue;
            }
            
            if (row.size() != expectedFields) {
                errors.add(String.format("数据数组第 %d 行字段数量不一致", i + 1));
            }
        }
    }
    
    /**
     * 验证元数据结构
     */
    private void validateMetadata(JsonNode metadata, List<String> errors) {
        if (!metadata.isObject()) {
            errors.add("metadata应该是对象类型");
            return;
        }
        
        // 检查列定义
        if (metadata.has("columns")) {
            JsonNode columns = metadata.get("columns");
            if (!columns.isArray()) {
                errors.add("metadata.columns应该是数组类型");
            } else {
                for (int i = 0; i < columns.size(); i++) {
                    JsonNode column = columns.get(i);
                    if (!column.has("name") || column.get("name").asText().trim().isEmpty()) {
                        errors.add(String.format("列定义第 %d 项缺少name字段", i + 1));
                    }
                }
            }
        }
    }
    
    /**
     * 验证结果内部类
     */
    public static class ValidationResult {
        private final boolean valid;
        private final List<String> errors;
        
        public ValidationResult(boolean valid, List<String> errors) {
            this.valid = valid;
            this.errors = errors != null ? errors : new ArrayList<>();
        }
        
        public boolean isValid() {
            return valid;
        }
        
        public List<String> getErrors() {
            return errors;
        }
        
        public String getErrorMessage() {
            return String.join("; ", errors);
        }
    }
}