package com.flowmining.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.reactive.function.client.WebClientResponseException;
import reactor.core.publisher.Mono;
import reactor.util.retry.Retry;

import java.time.Duration;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * AI客户端服务
 */
@Service
public class AIClientService {
    
    private static final Logger logger = LoggerFactory.getLogger(AIClientService.class);
    
    @Value("${ai.api.key:sk-}")
    private String apiKey;
    
    @Value("${ai.api.base-url:https://dashscope.aliyuncs.com/compatible-mode/v1}")
    private String baseUrl;
    
    @Value("${ai.model.name:qwen-turbo}")
    private String defaultModel;
    
    @Value("${ai.model.max-tokens:2000}")
    private Integer defaultMaxTokens;
    
    @Value("${ai.model.temperature:0.7}")
    private Double defaultTemperature;
    
    @Value("${ai.rate-limit.interval:1000}")
    private Long rateLimitInterval;
    
    private final WebClient webClient;
    private final ObjectMapper objectMapper;
    private long lastRequestTime = 0;
    
    public AIClientService() {
        this.webClient = WebClient.builder()
                .codecs(configurer -> configurer.defaultCodecs().maxInMemorySize(10 * 1024 * 1024))
                .build();
        this.objectMapper = new ObjectMapper();
    }
    
    /**
     * 生成AI完成内容
     */
    public CompletableFuture<String> generateCompletion(String prompt) {
        return generateCompletion(prompt, defaultModel, defaultMaxTokens, defaultTemperature, 3);
    }
    
    /**
     * 生成AI完成内容（带参数）
     */
    public CompletableFuture<String> generateCompletion(String prompt, String model, Integer maxTokens, 
                                                       Double temperature, int retryCount) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                // 实施速率限制
                rateLimitWait();
                
                // 构建请求体
                Map<String, Object> requestBody = buildRequestBody(prompt, model, maxTokens, temperature);
                
                // 发送请求
                Mono<String> response = webClient.post()
                        .uri(baseUrl + "/chat/completions")
                        .header("Authorization", "Bearer " + apiKey)
                        .header("Content-Type", "application/json")
                        .bodyValue(requestBody)
                        .retrieve()
                        .bodyToMono(String.class)
                        .retryWhen(Retry.backoff(retryCount, Duration.ofSeconds(1))
                                .filter(throwable -> throwable instanceof WebClientResponseException))
                        .doOnError(error -> logger.error("AI API请求失败: {}", error.getMessage()));
                
                String responseBody = response.block(Duration.ofMinutes(2));
                return parseResponse(responseBody);
                
            } catch (Exception e) {
                logger.error("生成AI内容失败: {}", e.getMessage(), e);
                throw new RuntimeException("生成AI内容失败: " + e.getMessage(), e);
            }
        });
    }
    
    /**
     * 批量生成内容
     */
    public CompletableFuture<List<String>> batchGenerate(List<String> prompts) {
        List<CompletableFuture<String>> futures = prompts.stream()
                .map(this::generateCompletion)
                .toList();
        
        return CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))
                .thenApply(v -> futures.stream()
                        .map(CompletableFuture::join)
                        .toList());
    }
    
    /**
     * 构建请求体
     */
    private Map<String, Object> buildRequestBody(String prompt, String model, Integer maxTokens, Double temperature) {
        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("model", model);
        requestBody.put("max_tokens", maxTokens);
        requestBody.put("temperature", temperature);
        requestBody.put("stream", false);
        
        // 构建消息数组
        Map<String, String> message = new HashMap<>();
        message.put("role", "user");
        message.put("content", prompt);
        requestBody.put("messages", List.of(message));
        
        return requestBody;
    }
    
    /**
     * 解析AI响应
     */
    private String parseResponse(String responseBody) {
        try {
            JsonNode responseJson = objectMapper.readTree(responseBody);
            
            // 检查错误
            if (responseJson.has("error")) {
                JsonNode error = responseJson.get("error");
                String errorMessage = error.has("message") ? error.get("message").asText() : "未知错误";
                throw new RuntimeException("AI API错误: " + errorMessage);
            }
            
            // 提取内容
            if (responseJson.has("choices") && responseJson.get("choices").isArray()) {
                JsonNode choices = responseJson.get("choices");
                if (choices.size() > 0) {
                    JsonNode firstChoice = choices.get(0);
                    if (firstChoice.has("message") && firstChoice.get("message").has("content")) {
                        return firstChoice.get("message").get("content").asText();
                    }
                }
            }
            
            throw new RuntimeException("AI响应格式不正确");
            
        } catch (Exception e) {
            logger.error("解析AI响应失败: {}", e.getMessage());
            throw new RuntimeException("解析AI响应失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 实施速率限制
     */
    private void rateLimitWait() {
        long currentTime = System.currentTimeMillis();
        long timeSinceLastRequest = currentTime - lastRequestTime;
        
        if (timeSinceLastRequest < rateLimitInterval) {
            try {
                long waitTime = rateLimitInterval - timeSinceLastRequest;
                logger.debug("速率限制等待 {} ms", waitTime);
                Thread.sleep(waitTime);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                logger.warn("速率限制等待被中断");
            }
        }
        
        lastRequestTime = System.currentTimeMillis();
    }
    
    /**
     * 检查AI服务是否可用
     */
    public boolean isServiceAvailable() {
        try {
            String testPrompt = "Hello";
            CompletableFuture<String> future = generateCompletion(testPrompt, defaultModel, 10, 0.1, 1);
            String result = future.get();
            return result != null && !result.trim().isEmpty();
        } catch (Exception e) {
            logger.warn("AI服务不可用: {}", e.getMessage());
            return false;
        }
    }
    
    // Getter方法
    public String getApiKey() {
        return apiKey;
    }
    
    public String getBaseUrl() {
        return baseUrl;
    }
    
    public String getDefaultModel() {
        return defaultModel;
    }
    
    public Integer getDefaultMaxTokens() {
        return defaultMaxTokens;
    }
    
    public Double getDefaultTemperature() {
        return defaultTemperature;
    }
}