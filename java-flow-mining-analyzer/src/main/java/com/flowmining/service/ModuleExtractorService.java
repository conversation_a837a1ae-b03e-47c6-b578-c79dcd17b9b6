package com.flowmining.service;

import com.fasterxml.jackson.databind.JsonNode;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 模块提取服务
 */
@Service
public class ModuleExtractorService {
    
    private static final Logger logger = LoggerFactory.getLogger(ModuleExtractorService.class);
    
    /**
     * 从数据中提取并分类模块
     */
    public ModuleExtractionResult extractModules(List<JsonNode> data) {
        List<JsonNode> regularModules = new ArrayList<>();
        List<JsonNode> simpleModules = new ArrayList<>();
        
        for (JsonNode module : data) {
            if (isSimpleModule(module)) {
                simpleModules.add(module);
            } else {
                regularModules.add(module);
            }
        }
        
        logger.info("提取到 {} 个常规模块和 {} 个简单模块", regularModules.size(), simpleModules.size());
        
        return new ModuleExtractionResult(regularModules, simpleModules);
    }
    
    /**
     * 判断是否为简单模块
     */
    private boolean isSimpleModule(JsonNode module) {
        return module.has("simple") && module.get("simple").asBoolean();
    }
    
    /**
     * 按类型分类模块
     */
    public Map<String, List<JsonNode>> categorizeModules(List<JsonNode> modules) {
        Map<String, List<JsonNode>> categorized = new HashMap<>();
        categorized.put("data_table", new ArrayList<>());
        categorized.put("kpi_indicator", new ArrayList<>());
        categorized.put("process_flow", new ArrayList<>());
        categorized.put("variant_analysis", new ArrayList<>());
        categorized.put("other", new ArrayList<>());
        
        for (JsonNode module : modules) {
            String type = determineModuleType(module);
            categorized.get(type).add(module);
        }
        
        return categorized;
    }
    
    /**
     * 确定模块类型
     */
    private String determineModuleType(JsonNode module) {
        // 检查是否有明确的类型字段
        if (module.has("type")) {
            String type = module.get("type").asText().toLowerCase();
            switch (type) {
                case "data_table":
                case "table":
                    return "data_table";
                case "kpi_indicator":
                case "kpi":
                    return "kpi_indicator";
                case "process_flow":
                case "flow":
                    return "process_flow";
                case "variant_analysis":
                case "variant":
                    return "variant_analysis";
                default:
                    return "other";
            }
        }
        
        // 根据数据结构推断类型
        if (module.has("kpiValue") || (module.has("simple") && module.get("simple").asBoolean())) {
            return "kpi_indicator";
        }
        
        if (module.has("data") && module.get("data").isArray()) {
            return "data_table";
        }
        
        if (module.has("flow") || module.has("steps") || module.has("nodes")) {
            return "process_flow";
        }
        
        if (module.has("variants") || module.has("paths")) {
            return "variant_analysis";
        }
        
        return "other";
    }
    
    /**
     * 获取模块的显示标题
     */
    public String getModuleDisplayTitle(JsonNode module) {
        if (module.has("title")) {
            return module.get("title").asText();
        }
        
        if (module.has("name")) {
            return module.get("name").asText();
        }
        
        // 根据类型生成默认标题
        String type = determineModuleType(module);
        switch (type) {
            case "data_table":
                return "数据表格";
            case "kpi_indicator":
                return "关键指标";
            case "process_flow":
                return "流程图";
            case "variant_analysis":
                return "变体分析";
            default:
                return "分析模块";
        }
    }
    
    /**
     * 模块提取结果
     */
    public static class ModuleExtractionResult {
        private final List<JsonNode> regularModules;
        private final List<JsonNode> simpleModules;
        
        public ModuleExtractionResult(List<JsonNode> regularModules, List<JsonNode> simpleModules) {
            this.regularModules = regularModules;
            this.simpleModules = simpleModules;
        }
        
        public List<JsonNode> getRegularModules() {
            return regularModules;
        }
        
        public List<JsonNode> getSimpleModules() {
            return simpleModules;
        }
        
        public int getTotalModules() {
            return regularModules.size() + simpleModules.size();
        }
    }
}