package com.flowmining.service;

import org.springframework.stereotype.Service;

/**
 * 提示词模板服务
 */
@Service
public class PromptTemplateService {
    
    /**
     * 构建模块分析提示
     */
    public String buildAnalysisPrompt(String title, String customPrompt, String moduleData, String domain) {
        StringBuilder prompt = new StringBuilder();
        
        prompt.append("你是一个专业的流程挖掘分析师。");
        
        if (domain != null && !domain.trim().isEmpty()) {
            prompt.append("你具有").append(domain).append("领域的专业知识。");
        }
        
        prompt.append("\n\n请分析以下流程挖掘数据，生成专业的分析报告内容：\n\n");
        prompt.append("模块标题：").append(title).append("\n\n");
        prompt.append("数据内容：\n").append(moduleData).append("\n\n");
        
        prompt.append("分析要求：\n");
        prompt.append(customPrompt).append("\n\n");
        
        prompt.append("请按照以下格式输出分析内容：\n");
        prompt.append("• 第一个分析点\n");
        prompt.append("• 第二个分析点\n");
        prompt.append("• 第三个分析点\n\n");
        
        prompt.append("注意事项：\n");
        prompt.append("1. 分析要基于实际数据，提供具体的数字支撑\n");
        prompt.append("2. 语言要专业但易懂，适合商业报告\n");
        prompt.append("3. 重点关注业务价值和实际应用\n");
        prompt.append("4. 每个分析点应该简洁明了，突出重点");
        
        return prompt.toString();
    }
    
    /**
     * 构建简单模块分析提示
     */
    public String buildSimpleModulesPrompt(String modulesData, String domain) {
        StringBuilder prompt = new StringBuilder();
        
        prompt.append("你是一个专业的数据分析师。");
        
        if (domain != null && !domain.trim().isEmpty()) {
            prompt.append("你具有").append(domain).append("领域的专业知识。");
        }
        
        prompt.append("\n\n请分析以下关键业务指标：\n\n");
        prompt.append(modulesData).append("\n\n");
        
        prompt.append("请生成专业的指标分析内容，按照以下格式：\n");
        prompt.append("• 指标概述分析\n");
        prompt.append("• 关键发现和趋势\n");
        prompt.append("• 业务改进建议\n\n");
        
        prompt.append("分析要求：\n");
        prompt.append("1. 分析要简洁专业，突出业务价值\n");
        prompt.append("2. 重点关注指标的实际意义和影响\n");
        prompt.append("3. 提供可操作的改进建议");
        
        return prompt.toString();
    }
    
    /**
     * 构建报告摘要提示
     */
    public String buildSummaryPrompt(String modulesInfo, String dataOverview, String domain) {
        StringBuilder prompt = new StringBuilder();
        
        prompt.append("你是一个资深的商业分析顾问。");
        
        if (domain != null && !domain.trim().isEmpty()) {
            prompt.append("你具有").append(domain).append("领域的丰富经验。");
        }
        
        prompt.append("\n\n请为以下流程挖掘分析报告生成执行摘要：\n\n");
        prompt.append("报告模块：").append(modulesInfo).append("\n");
        prompt.append("数据概况：").append(dataOverview).append("\n\n");
        
        prompt.append("请生成高质量的执行摘要，包含以下内容：\n");
        prompt.append("• 分析目标和范围\n");
        prompt.append("• 核心发现和洞察\n");
        prompt.append("• 关键建议和行动计划\n\n");
        
        prompt.append("摘要要求：\n");
        prompt.append("1. 内容要高度概括，突出重点\n");
        prompt.append("2. 语言要专业严谨，适合高层管理者阅读\n");
        prompt.append("3. 重点强调业务价值和实施建议\n");
        prompt.append("4. 每个要点都要有实际意义和可操作性");
        
        return prompt.toString();
    }
    
    /**
     * 构建标题优化提示
     */
    public String buildTitlePolishPrompt(String originalTitle, String domain) {
        StringBuilder prompt = new StringBuilder();
        
        prompt.append("请将以下标题优化为更专业、更具商业价值的表达：\n\n");
        prompt.append("原标题：").append(originalTitle).append("\n\n");
        
        if (domain != null && !domain.trim().isEmpty()) {
            prompt.append("领域背景：").append(domain).append("\n\n");
        }
        
        prompt.append("优化要求：\n");
        prompt.append("1. 保持原意，但使用更专业的术语\n");
        prompt.append("2. 标题要简洁明了，突出重点\n");
        prompt.append("3. 适合商业报告的正式语言\n");
        prompt.append("4. 长度控制在15个字以内\n\n");
        
        prompt.append("请直接返回优化后的标题，不需要其他说明。");
        
        return prompt.toString();
    }
    
    /**
     * 构建简单模块标题生成提示
     */
    public String buildSimpleTitlePrompt(List<com.fasterxml.jackson.databind.JsonNode> modules, String domain) {
        StringBuilder prompt = new StringBuilder();
        
        prompt.append("请为以下KPI指标模块生成一个合适的章节标题：\n\n");
        
        for (int i = 0; i < modules.size(); i++) {
            com.fasterxml.jackson.databind.JsonNode module = modules.get(i);
            String title = module.has("title") ? module.get("title").asText() : "指标" + (i + 1);
            prompt.append("- ").append(title).append("\n");
        }
        
        if (domain != null && !domain.trim().isEmpty()) {
            prompt.append("\n领域背景：").append(domain);
        }
        
        prompt.append("\n\n要求：\n");
        prompt.append("1. 标题要能概括所有指标的共同特征\n");
        prompt.append("2. 使用专业但易懂的语言\n");
        prompt.append("3. 标题长度控制在10个字以内\n");
        prompt.append("4. 直接返回标题，不需要其他说明");
        
        return prompt.toString();
    }
}