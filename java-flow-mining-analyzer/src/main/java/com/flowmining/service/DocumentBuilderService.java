package com.flowmining.service;

import org.apache.poi.util.Units;
import org.apache.poi.wp.usermodel.HeaderFooterType;
import org.apache.poi.xwpf.usermodel.*;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTTabs;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.STTabJc;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.io.ByteArrayInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Base64;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Word文档构建服务
 */
@Service
public class DocumentBuilderService {
    
    private static final Logger logger = LoggerFactory.getLogger(DocumentBuilderService.class);
    
    private XWPFDocument document;
    private String outputPath;
    private int currentLevel = 1;
    private int[] sectionNumbers = new int[]{0, 0, 0}; // 支持三级编号
    
    /**
     * 初始化文档
     */
    public void initializeDocument(String outputPath) {
        this.outputPath = outputPath;
        this.document = new XWPFDocument();
        setupDocumentStyles();
        logger.info("初始化Word文档: {}", outputPath);
    }
    
    /**
     * 设置文档样式
     */
    private void setupDocumentStyles() {
        // 设置页面边距等基本样式
        // Apache POI对样式的支持有限，这里主要设置基本格式
    }
    
    /**
     * 添加标题页
     */
    public void addTitlePage(String title, String subtitle) {
        XWPFParagraph titlePara = document.createParagraph();
        titlePara.setAlignment(ParagraphAlignment.CENTER);
        titlePara.setSpacingAfter(600);
        
        XWPFRun titleRun = titlePara.createRun();
        titleRun.setText(title != null ? title : "流程挖掘分析报告");
        titleRun.setBold(true);
        titleRun.setFontSize(24);
        titleRun.setFontFamily("宋体");
        
        if (subtitle != null && !subtitle.trim().isEmpty()) {
            XWPFParagraph subtitlePara = document.createParagraph();
            subtitlePara.setAlignment(ParagraphAlignment.CENTER);
            subtitlePara.setSpacingAfter(400);
            
            XWPFRun subtitleRun = subtitlePara.createRun();
            subtitleRun.setText(subtitle);
            subtitleRun.setFontSize(16);
            subtitleRun.setFontFamily("宋体");
        }
        
        // 添加生成时间
        XWPFParagraph datePara = document.createParagraph();
        datePara.setAlignment(ParagraphAlignment.CENTER);
        datePara.setSpacingAfter(800);
        
        XWPFRun dateRun = datePara.createRun();
        String dateStr = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy年MM月dd日"));
        dateRun.setText("生成时间：" + dateStr);
        dateRun.setFontSize(12);
        dateRun.setFontFamily("宋体");
        
        // 添加分页符
        addPageBreak();
        
        logger.info("添加标题页完成");
    }
    
    /**
     * 添加目录
     */
    public void addTableOfContents() {
        XWPFParagraph tocTitle = document.createParagraph();
        tocTitle.setAlignment(ParagraphAlignment.CENTER);
        tocTitle.setSpacingAfter(400);
        
        XWPFRun tocTitleRun = tocTitle.createRun();
        tocTitleRun.setText("目    录");
        tocTitleRun.setBold(true);
        tocTitleRun.setFontSize(18);
        tocTitleRun.setFontFamily("宋体");
        
        // 添加目录占位符
        XWPFParagraph tocPlaceholder = document.createParagraph();
        XWPFRun placeholderRun = tocPlaceholder.createRun();
        placeholderRun.setText("{TOC}");
        placeholderRun.setItalic(true);
        placeholderRun.setFontFamily("宋体");
        
        addPageBreak();
        
        logger.info("添加目录占位符完成");
    }
    
    /**
     * 添加章节
     */
    public void addSection(String title, String content, int level) {
        if (level < 1 || level > 3) {
            level = 1;
        }
        
        updateSectionNumbers(level);
        String numberedTitle = formatSectionTitle(title, level);
        
        // 添加章节标题
        XWPFParagraph titlePara = document.createParagraph();
        titlePara.setSpacingBefore(level == 1 ? 400 : 200);
        titlePara.setSpacingAfter(200);
        
        XWPFRun titleRun = titlePara.createRun();
        titleRun.setText(numberedTitle);
        titleRun.setBold(true);
        titleRun.setFontSize(level == 1 ? 16 : (level == 2 ? 14 : 12));
        titleRun.setFontFamily("宋体");
        
        // 添加内容
        if (content != null && !content.trim().isEmpty()) {
            parseAndAddContent(content);
        }
        
        logger.debug("添加章节: {}", numberedTitle);
    }
    
    /**
     * 添加执行摘要
     */
    public void addExecutiveSummary(String summaryContent) {
        addSection("报告摘要", summaryContent, 1);
        logger.info("添加执行摘要完成");
    }
    
    /**
     * 添加图片（从Base64）
     */
    public void addImageFromBase64(String base64String, int widthInPixels) {
        try {
            // 移除Base64前缀
            String cleanBase64 = base64String.replaceFirst("^data:image/[^;]+;base64,", "");
            byte[] imageBytes = Base64.getDecoder().decode(cleanBase64);
            
            XWPFParagraph imagePara = document.createParagraph();
            imagePara.setAlignment(ParagraphAlignment.CENTER);
            
            XWPFRun imageRun = imagePara.createRun();
            imageRun.addPicture(
                new ByteArrayInputStream(imageBytes),
                XWPFDocument.PICTURE_TYPE_PNG,
                "image.png",
                Units.pixelToEMU(widthInPixels),
                Units.pixelToEMU(widthInPixels * 3 / 4) // 假设4:3比例
            );
            
            logger.debug("添加图片完成，宽度: {}px", widthInPixels);
            
        } catch (Exception e) {
            logger.error("添加图片失败: {}", e.getMessage());
            // 添加图片加载失败的提示
            XWPFParagraph errorPara = document.createParagraph();
            XWPFRun errorRun = errorPara.createRun();
            errorRun.setText("[图片加载失败]");
            errorRun.setItalic(true);
        }
    }
    
    /**
     * 保存文档
     */
    public String save() throws IOException {
        try (FileOutputStream out = new FileOutputStream(outputPath)) {
            document.write(out);
            logger.info("文档保存成功: {}", outputPath);
            return outputPath;
        } catch (IOException e) {
            logger.error("保存文档失败: {}", e.getMessage());
            throw e;
        } finally {
            if (document != null) {
                try {
                    document.close();
                } catch (IOException e) {
                    logger.warn("关闭文档时出错: {}", e.getMessage());
                }
            }
        }
    }
    
    /**
     * 更新章节编号
     */
    private void updateSectionNumbers(int level) {
        sectionNumbers[level - 1]++;
        
        // 重置下级编号
        for (int i = level; i < sectionNumbers.length; i++) {
            sectionNumbers[i] = 0;
        }
    }
    
    /**
     * 格式化章节标题
     */
    private String formatSectionTitle(String title, int level) {
        String[] chineseNumbers = {"一", "二", "三", "四", "五", "六", "七", "八", "九", "十"};
        
        switch (level) {
            case 1:
                if (sectionNumbers[0] <= chineseNumbers.length) {
                    return chineseNumbers[sectionNumbers[0] - 1] + "、" + title;
                } else {
                    return sectionNumbers[0] + "、" + title;
                }
            case 2:
                return "（" + sectionNumbers[1] + "）" + title;
            case 3:
                return sectionNumbers[2] + ". " + title;
            default:
                return title;
        }
    }
    
    /**
     * 解析并添加内容
     */
    private void parseAndAddContent(String content) {
        if (content == null || content.trim().isEmpty()) {
            return;
        }
        
        String[] lines = content.split("\\n");
        for (String line : lines) {
            line = line.trim();
            if (line.isEmpty()) {
                continue;
            }
            
            XWPFParagraph para = document.createParagraph();
            para.setSpacingAfter(150);
            para.setIndentationLeft(400); // 缩进
            
            XWPFRun run = para.createRun();
            run.setText(line);
            run.setFontSize(12);
            run.setFontFamily("宋体");
        }
    }
    
    /**
     * 添加分页符
     */
    private void addPageBreak() {
        XWPFParagraph pageBreak = document.createParagraph();
        XWPFRun run = pageBreak.createRun();
        run.addBreak(BreakType.PAGE);
    }
    
    /**
     * 获取当前文档
     */
    public XWPFDocument getDocument() {
        return document;
    }
    
    /**
     * 获取输出路径
     */
    public String getOutputPath() {
        return outputPath;
    }
}