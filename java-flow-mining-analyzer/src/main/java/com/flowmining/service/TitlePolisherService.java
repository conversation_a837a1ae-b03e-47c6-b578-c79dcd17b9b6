package com.flowmining.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.concurrent.CompletableFuture;

/**
 * 标题优化服务
 */
@Service
public class TitlePolisherService {
    
    private static final Logger logger = LoggerFactory.getLogger(TitlePolisherService.class);
    
    @Autowired
    private AIClientService aiClientService;
    
    @Autowired
    private PromptTemplateService promptTemplateService;
    
    /**
     * 优化标题
     */
    public CompletableFuture<String> polishTitle(String originalTitle, String domain) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                if (originalTitle == null || originalTitle.trim().isEmpty()) {
                    return "分析模块";
                }
                
                String prompt = promptTemplateService.buildTitlePolishPrompt(originalTitle, domain);
                
                logger.debug("开始优化标题: {}", originalTitle);
                
                String polishedTitle = aiClientService.generateCompletion(prompt).join();
                
                // 清理返回的标题
                polishedTitle = cleanTitle(polishedTitle);
                
                // 如果优化后的标题为空或过长，则使用原标题
                if (polishedTitle.isEmpty() || polishedTitle.length() > 20) {
                    logger.warn("标题优化结果不合适，使用原标题: {}", originalTitle);
                    return originalTitle;
                }
                
                logger.debug("标题优化完成: {} -> {}", originalTitle, polishedTitle);
                return polishedTitle;
                
            } catch (Exception e) {
                logger.warn("标题优化失败，使用原标题: {}, 错误: {}", originalTitle, e.getMessage());
                return originalTitle;
            }
        });
    }
    
    /**
     * 批量优化标题
     */
    public CompletableFuture<java.util.List<String>> polishTitles(java.util.List<String> titles, String domain) {
        java.util.List<CompletableFuture<String>> futures = titles.stream()
                .map(title -> polishTitle(title, domain))
                .toList();
        
        return CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))
                .thenApply(v -> futures.stream()
                        .map(CompletableFuture::join)
                        .toList());
    }
    
    /**
     * 清理标题文本
     */
    private String cleanTitle(String title) {
        if (title == null) {
            return "";
        }
        
        // 移除多余的引号、括号等
        title = title.replaceAll("^[\"'『』「」《》]+|[\"'『』「」《》]+$", "");
        title = title.replaceAll("^标题[:：]?\\s*", "");
        title = title.replaceAll("^优化后的?标题[:：]?\\s*", "");
        
        // 移除换行符
        title = title.replaceAll("\\r?\\n", " ");
        
        // 移除多余空格
        title = title.trim().replaceAll("\\s+", " ");
        
        return title;
    }
    
    /**
     * 生成简单模块的章节标题
     */
    public CompletableFuture<String> generateSimpleModuleTitle(java.util.List<com.fasterxml.jackson.databind.JsonNode> modules, String domain) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                String prompt = promptTemplateService.buildSimpleTitlePrompt(modules, domain);
                
                logger.debug("开始生成简单模块标题");
                
                String generatedTitle = aiClientService.generateCompletion(prompt).join();
                generatedTitle = cleanTitle(generatedTitle);
                
                // 如果生成失败，使用默认标题
                if (generatedTitle.isEmpty()) {
                    generatedTitle = "关键业务指标";
                }
                
                logger.debug("简单模块标题生成完成: {}", generatedTitle);
                return generatedTitle;
                
            } catch (Exception e) {
                logger.warn("生成简单模块标题失败，使用默认标题: {}", e.getMessage());
                return "关键业务指标";
            }
        });
    }
}