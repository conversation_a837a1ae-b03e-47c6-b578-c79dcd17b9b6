package com.flowmining.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.flowmining.model.TaskInfo;
import com.flowmining.model.TaskStatus;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.File;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * 报告处理服务
 */
@Service
public class ReportProcessorService {
    
    private static final Logger logger = LoggerFactory.getLogger(ReportProcessorService.class);
    
    @Autowired
    private JsonLoaderService jsonLoaderService;
    
    @Autowired
    private JsonValidatorService jsonValidatorService;
    
    @Autowired
    private ModuleExtractorService moduleExtractorService;
    
    @Autowired
    private TitlePolisherService titlePolisherService;
    
    @Autowired
    private ContentGeneratorService contentGeneratorService;
    
    @Autowired
    private DocumentBuilderService documentBuilderService;
    
    @Value("${app.output.directory:outputs}")
    private String outputDirectory;
    
    @Value("${app.domain.default:通用}")
    private String defaultDomain;
    
    // 任务状态存储
    private final ConcurrentHashMap<String, TaskInfo> taskStorage = new ConcurrentHashMap<>();
    
    /**
     * 处理报告生成（异步）
     */
    public CompletableFuture<String> processReportAsync(String taskId, Object jsonData, String domain, String customPrompt) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                // 更新任务状态为处理中
                updateTaskStatus(taskId, TaskStatus.PROCESSING, "开始处理报告");
                
                // 处理JSON数据
                JsonNode dataNode = jsonLoaderService.fromObject(jsonData);
                List<JsonNode> normalizedData = jsonLoaderService.normalizeData(dataNode);
                
                // 验证数据
                JsonValidatorService.ValidationResult validation = jsonValidatorService.validateAllModules(normalizedData);
                if (!validation.isValid()) {
                    logger.warn("数据验证发现问题: {}", validation.getErrorMessage());
                }
                
                // 提取模块
                ModuleExtractorService.ModuleExtractionResult extraction = moduleExtractorService.extractModules(normalizedData);
                List<JsonNode> regularModules = extraction.getRegularModules();
                List<JsonNode> simpleModules = extraction.getSimpleModules();
                
                updateTaskStatus(taskId, TaskStatus.PROCESSING, "数据处理完成，开始生成内容");
                
                // 生成报告
                String outputPath = generateReport(taskId, regularModules, simpleModules, normalizedData, domain, customPrompt);
                
                // 更新任务状态为完成
                TaskInfo task = taskStorage.get(taskId);
                task.setStatus(TaskStatus.COMPLETED);
                task.setCompletedAt(LocalDateTime.now());
                task.setOutputFile(new File(outputPath).getName());
                task.setMessage("报告生成成功");
                
                logger.info("报告处理完成: {}", outputPath);
                return outputPath;
                
            } catch (Exception e) {
                logger.error("处理报告失败: {}", e.getMessage(), e);
                
                // 更新任务状态为失败
                TaskInfo task = taskStorage.get(taskId);
                if (task != null) {
                    task.setStatus(TaskStatus.FAILED);
                    task.setCompletedAt(LocalDateTime.now());
                    task.setError(e.getMessage());
                }
                
                throw new RuntimeException("处理报告失败: " + e.getMessage(), e);
            }
        });
    }
    
    /**
     * 生成报告
     */
    private String generateReport(String taskId, List<JsonNode> regularModules, List<JsonNode> simpleModules, 
                                 List<JsonNode> normalizedData, String domain, String customPrompt) throws Exception {
        
        // 确定使用的领域
        String effectiveDomain = (domain != null && !domain.trim().isEmpty()) ? domain : defaultDomain;
        
        // 生成输出文件路径
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
        String outputFilename = String.format("流程挖掘分析报告_%s_%s.docx", taskId.substring(0, 8), timestamp);
        String outputPath = new File(outputDirectory, outputFilename).getAbsolutePath();
        
        // 确保输出目录存在
        new File(outputDirectory).mkdirs();
        
        // 初始化文档构建器
        documentBuilderService.initializeDocument(outputPath);
        
        // 添加标题页和目录
        documentBuilderService.addTitlePage("流程挖掘分析报告", "基于AI智能分析生成");
        documentBuilderService.addTableOfContents();
        
        updateTaskStatus(taskId, TaskStatus.PROCESSING, "开始处理常规模块");
        
        // 处理常规模块
        for (JsonNode module : regularModules) {
            processRegularModule(module, effectiveDomain, customPrompt);
        }
        
        // 处理简单模块
        if (!simpleModules.isEmpty()) {
            updateTaskStatus(taskId, TaskStatus.PROCESSING, "开始处理简单模块");
            processSimpleModules(simpleModules, effectiveDomain);
        }
        
        // 生成报告摘要
        updateTaskStatus(taskId, TaskStatus.PROCESSING, "生成报告摘要");
        generateReportSummary(regularModules, simpleModules, normalizedData, effectiveDomain);
        
        // 保存文档
        updateTaskStatus(taskId, TaskStatus.PROCESSING, "保存文档");
        return documentBuilderService.save();
    }
    
    /**
     * 处理常规模块
     */
    private void processRegularModule(JsonNode module, String domain, String customPrompt) {
        try {
            String originalTitle = getModuleTitle(module);
            
            // 优化标题
            String polishedTitle = titlePolisherService.polishTitle(originalTitle, domain).join();
            
            // 生成内容
            String content = contentGeneratorService.generateModuleContent(module, polishedTitle, domain, customPrompt).join();
            
            // 添加到文档
            documentBuilderService.addSection(polishedTitle, content, 1);
            
            // 如果模块包含图片，添加图片
            if (module.has("image") && module.get("image").isTextual()) {
                String base64Image = module.get("image").asText();
                if (base64Image.startsWith("data:image")) {
                    documentBuilderService.addImageFromBase64(base64Image, 500);
                }
            }
            
        } catch (Exception e) {
            logger.error("处理常规模块失败: {}", e.getMessage());
            
            // 添加错误信息到文档
            String fallbackTitle = getModuleTitle(module);
            String fallbackContent = "• 该模块处理时出现错误，请检查数据格式\n• 错误信息：" + e.getMessage();
            documentBuilderService.addSection(fallbackTitle, fallbackContent, 1);
        }
    }
    
    /**
     * 处理简单模块
     */
    private void processSimpleModules(List<JsonNode> simpleModules, String domain) {
        try {
            // 生成章节标题
            String sectionTitle = titlePolisherService.generateSimpleModuleTitle(simpleModules, domain).join();
            
            // 生成内容
            String content = contentGeneratorService.generateSimpleModulesContent(simpleModules, domain).join();
            
            // 添加到文档
            documentBuilderService.addSection(sectionTitle, content, 1);
            
        } catch (Exception e) {
            logger.error("处理简单模块失败: {}", e.getMessage());
            
            // 添加错误信息到文档
            String fallbackContent = "• 关键指标数据处理时出现错误\n• 错误信息：" + e.getMessage();
            documentBuilderService.addSection("关键业务指标", fallbackContent, 1);
        }
    }
    
    /**
     * 生成报告摘要
     */
    private void generateReportSummary(List<JsonNode> regularModules, List<JsonNode> simpleModules, 
                                     List<JsonNode> normalizedData, String domain) {
        try {
            // 收集模块标题
            List<String> moduleTitles = regularModules.stream()
                    .map(this::getModuleTitle)
                    .collect(Collectors.toList());
            
            if (!simpleModules.isEmpty()) {
                moduleTitles.add("关键业务指标");
            }
            
            // 生成摘要内容
            String summaryContent = contentGeneratorService.generateReportSummary(moduleTitles, normalizedData, domain).join();
            
            // 添加到文档
            documentBuilderService.addExecutiveSummary(summaryContent);
            
        } catch (Exception e) {
            logger.error("生成报告摘要失败: {}", e.getMessage());
            
            // 添加默认摘要
            String fallbackSummary = "• 本报告基于流程挖掘数据分析生成\n• 包含多个分析维度和关键业务指标\n• 为业务优化提供数据支撑和改进建议";
            documentBuilderService.addExecutiveSummary(fallbackSummary);
        }
    }
    
    /**
     * 获取模块标题
     */
    private String getModuleTitle(JsonNode module) {
        if (module.has("title")) {
            return module.get("title").asText();
        }
        if (module.has("name")) {
            return module.get("name").asText();
        }
        return "分析模块";
    }
    
    /**
     * 更新任务状态
     */
    private void updateTaskStatus(String taskId, TaskStatus status, String message) {
        TaskInfo task = taskStorage.get(taskId);
        if (task != null) {
            task.setStatus(status);
            task.setMessage(message);
            logger.info("任务 {} 状态更新: {} - {}", taskId, status.getValue(), message);
        }
    }
    
    /**
     * 创建新任务
     */
    public TaskInfo createTask(String taskId) {
        TaskInfo task = new TaskInfo(taskId, TaskStatus.PENDING);
        task.setMessage("任务已创建，等待处理");
        taskStorage.put(taskId, task);
        logger.info("创建新任务: {}", taskId);
        return task;
    }
    
    /**
     * 获取任务信息
     */
    public TaskInfo getTaskInfo(String taskId) {
        return taskStorage.get(taskId);
    }
    
    /**
     * 获取所有任务
     */
    public List<TaskInfo> getAllTasks() {
        return taskStorage.values().stream()
                .sorted((a, b) -> b.getCreatedAt().compareTo(a.getCreatedAt()))
                .collect(Collectors.toList());
    }
    
    /**
     * 按状态过滤任务
     */
    public List<TaskInfo> getTasksByStatus(TaskStatus status) {
        return taskStorage.values().stream()
                .filter(task -> task.getStatus() == status)
                .sorted((a, b) -> b.getCreatedAt().compareTo(a.getCreatedAt()))
                .collect(Collectors.toList());
    }
    
    /**
     * 删除任务
     */
    public void deleteTask(String taskId) {
        TaskInfo task = taskStorage.remove(taskId);
        if (task != null) {
            logger.info("删除任务: {}", taskId);
        }
    }
}