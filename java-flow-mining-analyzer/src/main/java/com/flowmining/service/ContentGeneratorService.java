package com.flowmining.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * 内容生成服务
 */
@Service
public class ContentGeneratorService {
    
    private static final Logger logger = LoggerFactory.getLogger(ContentGeneratorService.class);
    
    @Autowired
    private AIClientService aiClientService;
    
    @Autowired
    private PromptTemplateService promptTemplateService;
    
    private final ObjectMapper objectMapper;
    
    public ContentGeneratorService() {
        this.objectMapper = new ObjectMapper();
    }
    
    /**
     * 为模块生成分析内容
     */
    public CompletableFuture<String> generateModuleContent(JsonNode module, String polishedTitle, String domain, String customPrompt) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                String title = getModuleTitle(module);
                String moduleData = formatModuleData(module);
                
                // 构建分析提示
                String prompt = promptTemplateService.buildAnalysisPrompt(title, customPrompt, moduleData, domain);
                
                logger.info("开始为模块 '{}' 生成分析内容", title);
                
                // 调用AI生成内容
                String rawContent = aiClientService.generateCompletion(prompt).join();
                
                // 清理和格式化内容
                String cleanedContent = cleanContent(rawContent);
                String formattedContent = fixNumbering(cleanedContent);
                
                logger.info("成功为模块 '{}' 生成分析内容", title);
                return formattedContent;
                
            } catch (Exception e) {
                logger.error("生成模块分析内容失败: {}", e.getMessage(), e);
                return generateFallbackContent(module);
            }
        });
    }
    
    /**
     * 为简单模块生成内容
     */
    public CompletableFuture<String> generateSimpleModulesContent(List<JsonNode> modules, String domain) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                String formattedModules = formatSimpleModules(modules);
                String prompt = promptTemplateService.buildSimpleModulesPrompt(formattedModules, domain);
                
                logger.info("开始为 {} 个简单模块生成分析内容", modules.size());
                
                String rawContent = aiClientService.generateCompletion(prompt).join();
                String cleanedContent = cleanContent(rawContent);
                String formattedContent = fixNumbering(cleanedContent);
                
                logger.info("成功为简单模块生成分析内容");
                return formattedContent;
                
            } catch (Exception e) {
                logger.error("生成简单模块分析内容失败: {}", e.getMessage(), e);
                return generateFallbackSimpleContent(modules);
            }
        });
    }
    
    /**
     * 生成报告摘要
     */
    public CompletableFuture<String> generateReportSummary(List<String> moduleTitles, List<JsonNode> normalizedData, String domain) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                String modulesInfo = String.join("、", moduleTitles);
                String dataOverview = generateDataOverview(normalizedData);
                
                String prompt = promptTemplateService.buildSummaryPrompt(modulesInfo, dataOverview, domain);
                
                logger.info("开始生成报告摘要");
                
                String rawContent = aiClientService.generateCompletion(prompt).join();
                String cleanedContent = cleanContent(rawContent);
                String formattedContent = fixNumbering(cleanedContent);
                
                logger.info("成功生成报告摘要");
                return formattedContent;
                
            } catch (Exception e) {
                logger.error("生成报告摘要失败: {}", e.getMessage(), e);
                return generateFallbackSummary(moduleTitles);
            }
        });
    }
    
    /**
     * 获取模块标题
     */
    private String getModuleTitle(JsonNode module) {
        if (module.has("title")) {
            return module.get("title").asText();
        }
        if (module.has("name")) {
            return module.get("name").asText();
        }
        return "未命名模块";
    }
    
    /**
     * 格式化模块数据
     */
    private String formatModuleData(JsonNode module) {
        try {
            return objectMapper.writerWithDefaultPrettyPrinter().writeValueAsString(module);
        } catch (Exception e) {
            logger.warn("格式化模块数据失败: {}", e.getMessage());
            return module.toString();
        }
    }
    
    /**
     * 格式化简单模块数据
     */
    private String formatSimpleModules(List<JsonNode> modules) {
        return modules.stream()
                .map(module -> {
                    String title = getModuleTitle(module);
                    String value = module.has("kpiValue") ? module.get("kpiValue").asText() : "无数据";
                    return String.format("%s: %s", title, value);
                })
                .collect(Collectors.joining("\n"));
    }
    
    /**
     * 生成数据概览
     */
    private String generateDataOverview(List<JsonNode> normalizedData) {
        int totalModules = normalizedData.size();
        long simpleModules = normalizedData.stream()
                .mapToLong(module -> module.has("simple") && module.get("simple").asBoolean() ? 1 : 0)
                .sum();
        long regularModules = totalModules - simpleModules;
        
        return String.format("本报告包含 %d 个分析模块，其中常规分析模块 %d 个，关键指标模块 %d 个。", 
                totalModules, regularModules, simpleModules);
    }
    
    /**
     * 清理内容（移除markdown格式）
     */
    private String cleanContent(String content) {
        if (StringUtils.isBlank(content)) {
            return "";
        }
        
        // 移除markdown格式
        content = content.replaceAll("\\*\\*(.*?)\\*\\*", "$1"); // 移除加粗
        content = content.replaceAll("\\*(.*?)\\*", "$1"); // 移除斜体
        content = content.replaceAll("```[\\s\\S]*?```", ""); // 移除代码块
        content = content.replaceAll("`(.*?)`", "$1"); // 移除行内代码
        content = content.replaceAll("#{1,6}\\s+", ""); // 移除标题标记
        
        // 清理多余的空行
        content = content.replaceAll("\\n{3,}", "\n\n");
        content = content.trim();
        
        return content;
    }
    
    /**
     * 修复编号格式
     */
    private String fixNumbering(String content) {
        if (StringUtils.isBlank(content)) {
            return "";
        }
        
        // 标准化列表编号
        content = content.replaceAll("^\\s*[\\d]+\\.", "• "); // 数字列表转为圆点
        content = content.replaceAll("^\\s*[-\\*]", "• "); // 统一列表符号
        
        return content;
    }
    
    /**
     * 生成后备内容（当AI生成失败时）
     */
    private String generateFallbackContent(JsonNode module) {
        String title = getModuleTitle(module);
        StringBuilder content = new StringBuilder();
        
        content.append("• 数据概况：本模块包含").append(title).append("相关数据\n");
        content.append("• 关键发现：数据结构完整，可进行进一步分析\n");
        content.append("• 建议措施：建议结合业务场景进行深入分析");
        
        return content.toString();
    }
    
    /**
     * 生成简单模块后备内容
     */
    private String generateFallbackSimpleContent(List<JsonNode> modules) {
        StringBuilder content = new StringBuilder();
        
        content.append("• 指标总览：本部分包含").append(modules.size()).append("个关键业务指标\n");
        content.append("• 数据状态：各项指标数据收集完整，展示了业务运营的关键表现\n");
        content.append("• 分析建议：建议定期监控这些关键指标的变化趋势，及时发现业务异常");
        
        return content.toString();
    }
    
    /**
     * 生成摘要后备内容
     */
    private String generateFallbackSummary(List<String> moduleTitles) {
        StringBuilder content = new StringBuilder();
        
        content.append("• 报告概述：本报告包含").append(moduleTitles.size()).append("个主要分析模块\n");
        content.append("• 分析范围：涵盖了流程挖掘的多个维度，提供了全面的业务洞察\n");
        content.append("• 总体结论：通过数据分析，为业务优化提供了有价值的参考信息");
        
        return content.toString();
    }
}