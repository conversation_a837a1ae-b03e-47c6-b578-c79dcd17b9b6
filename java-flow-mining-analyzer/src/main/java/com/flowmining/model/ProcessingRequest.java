package com.flowmining.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;
import java.util.Map;

/**
 * 处理请求模型
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class ProcessingRequest {
    
    @NotNull(message = "JSON数据不能为空")
    @JsonProperty("json_data")
    private Object jsonData;
    
    @JsonProperty("module_type")
    private String moduleType = "auto";
    
    @JsonProperty("output_format")
    private String outputFormat = "docx";
    
    private String domain;
    
    @Size(max = 1000, message = "提示词长度不能超过1000个字符")
    private String prompt;
    
    // 构造函数
    public ProcessingRequest() {}
    
    public ProcessingRequest(Object jsonData, String moduleType, String outputFormat, String domain, String prompt) {
        this.jsonData = jsonData;
        this.moduleType = moduleType;
        this.outputFormat = outputFormat;
        this.domain = domain;
        this.prompt = prompt;
    }
    
    // Getter和Setter方法
    public Object getJsonData() {
        return jsonData;
    }
    
    public void setJsonData(Object jsonData) {
        this.jsonData = jsonData;
    }
    
    public String getModuleType() {
        return moduleType;
    }
    
    public void setModuleType(String moduleType) {
        this.moduleType = moduleType;
    }
    
    public String getOutputFormat() {
        return outputFormat;
    }
    
    public void setOutputFormat(String outputFormat) {
        this.outputFormat = outputFormat;
    }
    
    public String getDomain() {
        return domain;
    }
    
    public void setDomain(String domain) {
        this.domain = domain;
    }
    
    public String getPrompt() {
        return prompt;
    }
    
    public void setPrompt(String prompt) {
        this.prompt = prompt;
    }
    
    /**
     * 获取有效的分析提示词
     * 如果用户提供了prompt，则使用用户的；否则使用默认提示词
     */
    public String getEffectivePrompt() {
        String defaultPrompt = "结合下面所有的数据进行深度分析并给出3条结论，结论中要有数据支撑，给出改善的意见";
        return (prompt != null && !prompt.trim().isEmpty()) ? prompt.trim() : defaultPrompt;
    }
    
    @Override
    public String toString() {
        return "ProcessingRequest{" +
                "jsonData=" + (jsonData != null ? jsonData.getClass().getSimpleName() : "null") +
                ", moduleType='" + moduleType + '\'' +
                ", outputFormat='" + outputFormat + '\'' +
                ", domain='" + domain + '\'' +
                ", prompt='" + (prompt != null ? prompt.substring(0, Math.min(prompt.length(), 50)) + "..." : "null") + '\'' +
                '}';
    }
}