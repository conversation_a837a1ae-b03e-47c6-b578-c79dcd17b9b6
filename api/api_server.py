from fastapi import FastAPI, HTTPException, BackgroundTasks
from fastapi.responses import FileResponse, HTMLResponse
from pydantic import BaseModel, field_validator
from typing import Optional, Dict, Any, Union, List  # 添加 Union 和 List
import os
import json
import uuid
from datetime import datetime
import shutil
import asyncio
from concurrent.futures import ThreadPoolExecutor
import time

# 添加项目根目录到路径
import sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.main import process_report
from src.utils.logging_utils import setup_logging
from config.ai_config import API_KEY, BASE_URL, MODEL_NAME, DEFAULT_MAX_TOKENS, DEFAULT_TEMPERATURE, DEFAULT_DOMAIN

from contextlib import asynccontextmanager

# 使用新的 lifespan 事件处理器
def check_dependencies():
    """检查必要的依赖是否已安装"""
    missing_packages = []
    
    try:
        import docx
    except ImportError:
        missing_packages.append("python-docx")
    
    try:
        import lxml
    except ImportError:
        missing_packages.append("lxml")
    
    if missing_packages:
        print(f"警告: 缺少以下依赖包，可能影响报告生成功能: {', '.join(missing_packages)}")
        print("请运行: pip install " + " ".join(missing_packages))
        return False
    
    return True

@asynccontextmanager
async def lifespan(app: FastAPI):
    # 启动时执行
    check_dependencies()
    asyncio.create_task(cleanup_old_files())
    yield
    # 关闭时执行（如果需要）
    pass

# 初始化FastAPI应用，使用lifespan
app = FastAPI(title="流程挖掘分析报告API", version="1.0.0", lifespan=lifespan)

# 设置日志
setup_logging()

# 创建必要的目录
UPLOAD_DIR = "uploads"
OUTPUT_DIR = "outputs"
TEMP_DIR = "temp"

for dir_path in [UPLOAD_DIR, OUTPUT_DIR, TEMP_DIR]:
    os.makedirs(dir_path, exist_ok=True)

# 任务状态存储
task_status = {}

# 创建线程池执行器
executor = ThreadPoolExecutor(max_workers=4)

# 数据模型
class AIConfig(BaseModel):
    api_key: Optional[str] = None
    base_url: Optional[str] = None
    model_name: Optional[str] = None
    max_tokens: Optional[int] = None
    temperature: Optional[float] = None
    domain: Optional[str] = None  # 添加领域参数
    
    model_config = {
        'protected_namespaces': ()  # 解决 pydantic 警告
    }

class TaskStatus(BaseModel):
    task_id: str
    status: str  # pending, processing, completed, failed
    created_at: datetime
    completed_at: Optional[datetime] = None
    output_file: Optional[str] = None
    error: Optional[str] = None

# 修改：新增JSON输入请求模型
class ProcessJsonRequest(BaseModel):
    json_data: Union[Dict[str, Any], List[Dict[str, Any]]]  # 接收JSON数据（对象或数组）
    module_type: Optional[str] = "auto"  # auto, regular, simple
    output_format: Optional[str] = "docx"  # docx, pdf (未来扩展)
    domain: Optional[str] = DEFAULT_DOMAIN  # 添加领域参数
    prompt: Optional[str] = None  # 用户自定义的分析提示词（可选参数）

    @field_validator('prompt')
    @classmethod
    def validate_prompt(cls, v):
        """验证prompt参数长度"""
        if v is not None and len(v) > 1000:
            raise ValueError('提示词长度不能超过1000个字符')
        return v

    def get_effective_prompt(self) -> str:
        """
        获取有效的分析提示词
        如果用户提供了prompt，则使用用户的；否则使用默认提示词
        """
        DEFAULT_PROMPT = "结合下面所有的数据进行深度分析并给出3条结论，结论中要有数据支撑，给出改善的意见"
        return self.prompt.strip() if self.prompt and self.prompt.strip() else DEFAULT_PROMPT

# 全局AI配置
ai_config = {
    "api_key": API_KEY,
    "base_url": BASE_URL,
    "model_name": MODEL_NAME,
    "max_tokens": DEFAULT_MAX_TOKENS,
    "temperature": DEFAULT_TEMPERATURE,
    "domain": DEFAULT_DOMAIN  # 添加领域参数
}

# HTML界面 - 更新以支持领域参数
HTML_CONTENT = """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>流程挖掘分析报告API测试界面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        
        h2 {
            color: #555;
            border-bottom: 2px solid #ddd;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        
        input[type="text"], textarea, select {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
            font-size: 14px;
        }
        
        textarea {
            min-height: 200px;
            resize: vertical;
        }
        
        button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin-right: 10px;
        }
        
        button:hover {
            background-color: #0056b3;
        }
        
        button.secondary {
            background-color: #6c757d;
        }
        
        button.secondary:hover {
            background-color: #545b62;
        }
        
        .success {
            color: #28a745;
            background-color: #d4edda;
            padding: 10px;
            border-radius: 4px;
            margin-top: 10px;
        }
        
        .error {
            color: #dc3545;
            background-color: #f8d7da;
            padding: 10px;
            border-radius: 4px;
            margin-top: 10px;
        }
        
        .info {
            color: #17a2b8;
            background-color: #d1ecf1;
            padding: 10px;
            border-radius: 4px;
            margin-top: 10px;
        }
        
        .task-list {
            max-height: 300px;
            overflow-y: auto;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 10px;
        }
        
        .task-item {
            padding: 10px;
            margin-bottom: 10px;
            background-color: #f8f9fa;
            border-radius: 4px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .status-pending { color: #ffc107; }
        .status-processing { color: #17a2b8; }
        .status-completed { color: #28a745; }
        .status-failed { color: #dc3545; }
        
        .config-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
        }
        
        @media (max-width: 768px) {
            .config-grid {
                grid-template-columns: 1fr;
            }
        }
        
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <h1>流程挖掘分析报告API测试界面</h1>
    
    <!-- 处理JSON数据 -->
    <div class="container">
        <h2>1. 处理JSON数据</h2>
        <div class="form-group">
            <label for="jsonData">JSON数据:</label>
            <textarea id="jsonData" placeholder='输入您的JSON数据，例如：
{
  "process": "订单处理",
  "steps": ["接收订单", "确认支付", "发货", "完成"],
  "data": "您的流程挖掘数据"
}'></textarea>
        </div>
        <div class="form-group">
            <label for="moduleType">模块类型:</label>
            <select id="moduleType">
                <option value="auto">自动</option>
                <option value="regular">常规</option>
                <option value="simple">简单</option>
            </select>
        </div>
        <div class="form-group">
            <label for="domain">领域:(可选，如金融、医疗、物流等)</label>
            <input type="text" id="domain" placeholder="输入分析领域，不填则为通用领域">
        </div>
        <div class="form-group">
            <label for="prompt">自定义分析提示词:(可选，最大1000字符)</label>
            <textarea id="prompt" placeholder="输入您的自定义分析提示词，例如：
请从成本控制角度分析这些数据，重点关注效率提升机会
不填则使用默认提示词：结合下面所有的数据进行深度分析并给出3条结论，结论中要有数据支撑，给出改善的意见" maxlength="1000" style="height: 80px;"></textarea>
        </div>
        <button onclick="processJson()">提交处理</button>
        <div id="processResult"></div>
    </div>
    
    <!-- 查询任务状态 -->
    <div class="container">
        <h2>2. 查询任务状态</h2>
        <div class="form-group">
            <label for="taskId">任务ID:</label>
            <input type="text" id="taskId" placeholder="输入任务ID">
        </div>
        <button onclick="checkStatus()">查询状态</button>
        <div id="statusResult"></div>
    </div>
    
    <!-- 下载报告 -->
    <div class="container">
        <h2>3. 下载报告</h2>
        <div class="form-group">
            <label for="downloadTaskId">任务ID:</label>
            <input type="text" id="downloadTaskId" placeholder="输入任务ID">
        </div>
        <button onclick="downloadReport()">下载报告</button>
        <div id="downloadResult"></div>
        <div class="info">
            <strong>提示:</strong> 如果生成的报告中目录显示为"{TOC}"，请在Word中打开文档后，右键点击目录区域，选择"更新域"更新目录。
        </div>
    </div>
    
    <!-- 任务列表 -->
    <div class="container">
        <h2>4. 任务列表</h2>
        <div class="form-group">
            <label for="statusFilter">状态过滤:</label>
            <select id="statusFilter">
                <option value="">全部</option>
                <option value="pending">待处理</option>
                <option value="processing">处理中</option>
                <option value="completed">已完成</option>
                <option value="failed">失败</option>
            </select>
        </div>
        <button onclick="listTasks()">刷新列表</button>
        <div id="taskList" class="task-list"></div>
    </div>
    
    <!-- AI配置 -->
    <div class="container">
        <h2>5. AI配置管理</h2>
        <button onclick="getConfig()">获取当前配置</button>
        <div id="currentConfig" style="margin-top: 10px;"></div>
        
        <h3>更新配置</h3>
        <div class="config-grid">
            <div class="form-group">
                <label for="apiKey">API Key:</label>
                <input type="text" id="apiKey" placeholder="输入新的API Key">
            </div>
            <div class="form-group">
                <label for="baseUrl">Base URL:</label>
                <input type="text" id="baseUrl" placeholder="输入新的Base URL">
            </div>
            <div class="form-group">
                <label for="modelName">模型名称:</label>
                <input type="text" id="modelName" placeholder="输入模型名称">
            </div>
            <div class="form-group">
                <label for="maxTokens">Max Tokens:</label>
                <input type="text" id="maxTokens" placeholder="输入最大令牌数">
            </div>
            <div class="form-group">
                <label for="temperature">Temperature:</label>
                <input type="text" id="temperature" placeholder="输入温度值(0-1)">
            </div>
            <div class="form-group">
                <label for="configDomain">默认领域:</label>
                <input type="text" id="configDomain" placeholder="输入默认领域，留空为通用领域">
            </div>
        </div>
        <button onclick="updateConfig()">更新配置</button>
        <div id="configResult"></div>
    </div>

    <script>
        // 使用相对路径
        const apiUrl = window.location.origin;
        
        // 处理JSON数据
        async function processJson() {
            try {
                const jsonData = document.getElementById('jsonData').value;
                const moduleType = document.getElementById('moduleType').value;
                const domain = document.getElementById('domain').value;
                const prompt = document.getElementById('prompt').value;
                
                if (!jsonData) {
                    showResult('processResult', '请输入JSON数据', 'error');
                    return;
                }
                
                let parsedData;
                try {
                    parsedData = JSON.parse(jsonData);
                } catch (e) {
                    showResult('processResult', 'JSON格式错误: ' + e.message, 'error');
                    return;
                }
                
                const requestBody = {
                    json_data: parsedData,
                    module_type: moduleType,
                    output_format: 'docx'
                };
                
                // 如果指定了领域，则添加到请求中
                if (domain) {
                    requestBody.domain = domain;
                }

                // 如果指定了自定义提示词，则添加到请求中
                if (prompt) {
                    requestBody.prompt = prompt;
                }
                
                const response = await fetch(`${apiUrl}/process_mining/process-json`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(requestBody)
                });
                
                const result = await response.json();
                if (response.ok) {
                    document.getElementById('taskId').value = result.task_id;
                    document.getElementById('downloadTaskId').value = result.task_id;
                    showResult('processResult', `成功！任务ID: ${result.task_id}<br>状态: ${result.status}<br>消息: ${result.message}`, 'success');
                } else {
                    // 显示更详细的错误信息
                    let errorMessage = `错误: ${result.detail || JSON.stringify(result)}`;
                    if (result.detail && Array.isArray(result.detail)) {
                        errorMessage = '验证错误：<br>' + result.detail.map(err => 
                            `字段: ${err.loc.join('.')} - ${err.msg}`
                        ).join('<br>');
                    }
                    showResult('processResult', errorMessage, 'error');
                }
            } catch (error) {
                showResult('processResult', `请求失败: ${error.message}`, 'error');
            }
        }
        
        // 查询任务状态
        async function checkStatus() {
            try {
                const taskId = document.getElementById('taskId').value;
                if (!taskId) {
                    showResult('statusResult', '请输入任务ID', 'error');
                    return;
                }
                
                const response = await fetch(`${apiUrl}/process_mining/task-status/${taskId}`);
                const result = await response.json();
                
                if (response.ok) {
                    const statusClass = `status-${result.status}`;
                    let html = `
                        <strong>任务ID:</strong> ${result.task_id}<br>
                        <strong>状态:</strong> <span class="${statusClass}">${result.status}</span><br>
                        <strong>创建时间:</strong> ${new Date(result.created_at).toLocaleString()}<br>
                    `;
                    
                    if (result.completed_at) {
                        html += `<strong>完成时间:</strong> ${new Date(result.completed_at).toLocaleString()}<br>`;
                    }
                    
                    if (result.output_file) {
                        html += `<strong>输出文件:</strong> ${result.output_file}<br>`;
                    }
                    
                    if (result.error) {
                        html += `<strong>错误信息:</strong> ${result.error}<br>`;
                    }
                    
                    showResult('statusResult', html, 'info');
                } else {
                    showResult('statusResult', `错误: ${result.detail || JSON.stringify(result)}`, 'error');
                }
            } catch (error) {
                showResult('statusResult', `请求失败: ${error.message}`, 'error');
            }
        }
        
        // 下载报告
        async function downloadReport() {
            try {
                const taskId = document.getElementById('downloadTaskId').value;
                if (!taskId) {
                    showResult('downloadResult', '请输入任务ID', 'error');
                    return;
                }
                
                window.open(`${apiUrl}/process_mining/download-report/${taskId}`, '_blank');
                showResult('downloadResult', '下载已开始，请检查浏览器下载', 'info');
            } catch (error) {
                showResult('downloadResult', `下载失败: ${error.message}`, 'error');
            }
        }
        
        // 列出任务
        async function listTasks() {
            try {
                const statusFilter = document.getElementById('statusFilter').value;
                const url = statusFilter 
                    ? `${apiUrl}/process_mining/tasks?status=${statusFilter}`
                    : `${apiUrl}/process_mining/tasks`;
                
                const response = await fetch(url);
                const result = await response.json();
                
                if (response.ok) {
                    const taskList = document.getElementById('taskList');
                    if (result.tasks.length === 0) {
                        taskList.innerHTML = '<p>没有找到任务</p>';
                    } else {
                        taskList.innerHTML = result.tasks.map(task => `
                            <div class="task-item">
                                <div>
                                    <strong>ID:</strong> ${task.task_id}<br>
                                    <strong>状态:</strong> <span class="status-${task.status}">${task.status}</span><br>
                                    <strong>创建时间:</strong> ${new Date(task.created_at).toLocaleString()}
                                </div>
                                <div>
                                    <button onclick="copyTaskId('${task.task_id}')">复制ID</button>
                                    ${task.status === 'completed' ? `<button onclick="document.getElementById('downloadTaskId').value='${task.task_id}';downloadReport()">下载</button>` : ''}
                                </div>
                            </div>
                        `).join('');
                    }
                } else {
                    showResult('taskList', `错误: ${result.detail || JSON.stringify(result)}`, 'error');
                }
            } catch (error) {
                showResult('taskList', `请求失败: ${error.message}`, 'error');
            }
        }
        
        // 获取AI配置
        async function getConfig() {
            try {
                const response = await fetch(`${apiUrl}/process_mining/ai-config`);
                const result = await response.json();
                
                if (response.ok) {
                    document.getElementById('currentConfig').innerHTML = `
                        <pre>${JSON.stringify(result, null, 2)}</pre>
                    `;
                } else {
                    showResult('currentConfig', `错误: ${result.detail || JSON.stringify(result)}`, 'error');
                }
            } catch (error) {
                showResult('currentConfig', `请求失败: ${error.message}`, 'error');
            }
        }
        
        // 更新AI配置
        async function updateConfig() {
            try {
                const config = {};
                
                const apiKey = document.getElementById('apiKey').value;
                const baseUrl = document.getElementById('baseUrl').value;
                const modelName = document.getElementById('modelName').value;
                const maxTokens = document.getElementById('maxTokens').value;
                const temperature = document.getElementById('temperature').value;
                const domain = document.getElementById('configDomain').value;
                
                if (apiKey) config.api_key = apiKey;
                if (baseUrl) config.base_url = baseUrl;
                if (modelName) config.model_name = modelName;
                if (maxTokens) config.max_tokens = parseInt(maxTokens);
                if (temperature) config.temperature = parseFloat(temperature);
                if (domain) config.domain = domain;
                
                const response = await fetch(`${apiUrl}/process_mining/ai-config`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(config)
                });
                
                const result = await response.json();
                
                if (response.ok) {
                    showResult('configResult', '配置更新成功！', 'success');
                    // 清空输入框
                    document.getElementById('apiKey').value = '';
                    document.getElementById('baseUrl').value = '';
                    document.getElementById('modelName').value = '';
                    document.getElementById('maxTokens').value = '';
                    document.getElementById('temperature').value = '';
                    document.getElementById('configDomain').value = '';
                    // 刷新当前配置
                    getConfig();
                } else {
                    showResult('configResult', `错误: ${result.detail || JSON.stringify(result)}`, 'error');
                }
            } catch (error) {
                showResult('configResult', `请求失败: ${error.message}`, 'error');
            }
        }
        
        // 复制任务ID
        function copyTaskId(taskId) {
            navigator.clipboard.writeText(taskId).then(() => {
                alert('任务ID已复制到剪贴板');
            });
        }
        
        // 显示结果
        function showResult(elementId, message, type) {
            const element = document.getElementById(elementId);
            element.className = type;
            element.innerHTML = message;
            
            // 3秒后自动隐藏成功消息
            if (type === 'success') {
                setTimeout(() => {
                    element.innerHTML = '';
                    element.className = '';
                }, 3000);
            }
        }
        
        // 页面加载时获取任务列表
        window.onload = function() {
            listTasks();
        };
    </script>
</body>
</html>
"""

@app.get("/", response_class=HTMLResponse)
async def root():
    """返回Web界面"""
    return HTML_CONTENT

# 修改：前缀从/api/改为/process_mining/，同时添加upload-json别名
@app.post("/process_mining/process-json")
@app.post("/process_mining/upload-json")  # 添加别名路由，解决404问题
async def process_json(
    background_tasks: BackgroundTasks,
    request: ProcessJsonRequest
):
    """
    接收JSON数据并开始处理
    
    返回任务ID，可用于查询状态和下载结果
    """
    # 添加日志
    print(f"Received request: {request}")
    
    # 生成任务ID
    task_id = str(uuid.uuid4())
    
    # 保存JSON数据到文件
    upload_path = os.path.join(UPLOAD_DIR, f"{task_id}.json")
    try:
        with open(upload_path, 'w', encoding='utf-8') as f:
            json.dump(request.json_data, f, ensure_ascii=False, indent=2)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"数据保存失败: {str(e)}")
    
    # 创建任务状态
    task_status[task_id] = TaskStatus(
        task_id=task_id,
        status="pending",
        created_at=datetime.now()
    )
    
    # 在后台处理报告生成
    background_tasks.add_task(process_report_async, task_id, upload_path, request.domain, request.get_effective_prompt())
    
    return {
        "task_id": task_id,
        "status": "pending",
        "message": "数据已接收，正在处理中"
    }

async def process_report_async(task_id: str, json_path: str, domain: Optional[str] = None, prompt: Optional[str] = None):
    """异步处理报告生成"""
    task_status[task_id].status = "processing"

    try:
        # 设置输出路径
        output_filename = f"report_{task_id}.docx"
        output_path = os.path.join(OUTPUT_DIR, output_filename)

        # 在线程池中运行同步的报告生成函数
        loop = asyncio.get_event_loop()
        await loop.run_in_executor(
            executor,
            process_report_with_config,
            json_path,
            output_path,
            ai_config,
            domain,
            prompt
        )
        
        # 更新任务状态
        task_status[task_id].status = "completed"
        task_status[task_id].completed_at = datetime.now()
        task_status[task_id].output_file = output_filename
        
    except Exception as e:
        # 更新错误状态
        task_status[task_id].status = "failed"
        task_status[task_id].error = str(e)
        task_status[task_id].completed_at = datetime.now()
        print(f"处理报告时出错: {str(e)}")

def process_report_with_config(json_path: str, output_path: str, config: Dict[str, Any], domain: Optional[str] = None, prompt: Optional[str] = None):
    """使用指定配置处理报告"""
    import config.ai_config as ai_config_module
    ai_config_module.API_KEY = config.get("api_key", ai_config_module.API_KEY)
    ai_config_module.BASE_URL = config.get("base_url", ai_config_module.BASE_URL)
    ai_config_module.MODEL_NAME = config.get("model_name", ai_config_module.MODEL_NAME)
    ai_config_module.DEFAULT_MAX_TOKENS = config.get("max_tokens", ai_config_module.DEFAULT_MAX_TOKENS)
    ai_config_module.DEFAULT_TEMPERATURE = config.get("temperature", ai_config_module.DEFAULT_TEMPERATURE)

    # 如果请求中指定了领域，则使用请求的领域，否则使用全局配置的领域
    domain_to_use = domain if domain is not None else config.get("domain", ai_config_module.DEFAULT_DOMAIN)

    result = process_report(json_path, output_path, domain_to_use, prompt)
    return result

# 修改所有路由的前缀
@app.get("/process_mining/task-status/{task_id}")
async def get_task_status(task_id: str):
    """查询任务状态"""
    if task_id not in task_status:
        raise HTTPException(status_code=404, detail="任务不存在")
    
    return task_status[task_id]

@app.get("/process_mining/download-report/{task_id}")
async def download_report(task_id: str):
    """下载生成的报告"""
    if task_id not in task_status:
        raise HTTPException(status_code=404, detail="任务不存在")
    task = task_status[task_id]
    if task.status != "completed":
        raise HTTPException(
            status_code=400, 
            detail=f"报告未完成，当前状态: {task.status}"
        )
    if not task.output_file:
        raise HTTPException(status_code=404, detail="输出文件不存在")
    file_path = os.path.join(OUTPUT_DIR, task.output_file)
    if not os.path.exists(file_path):
        raise HTTPException(status_code=404, detail="文件不存在")
    return FileResponse(
        path=file_path,
        filename=f"流程挖掘分析报告_{datetime.now().strftime('%Y%m%d')}.docx",
        media_type="application/vnd.openxmlformats-officedocument.wordprocessingml.document"
    )

@app.get("/process_mining/ai-config")
async def get_ai_config():
    """获取当前AI配置"""
    return AIConfig(**ai_config)

@app.post("/process_mining/ai-config")
async def update_ai_config(config: AIConfig):
    """更新AI配置"""
    global ai_config
    
    # 更新配置
    if config.api_key is not None:
        ai_config["api_key"] = config.api_key
    if config.base_url is not None:
        ai_config["base_url"] = config.base_url
    if config.model_name is not None:
        ai_config["model_name"] = config.model_name
    if config.max_tokens is not None:
        ai_config["max_tokens"] = config.max_tokens
    if config.temperature is not None:
        ai_config["temperature"] = config.temperature
    if config.domain is not None:
        ai_config["domain"] = config.domain
    
    return {
        "message": "配置已更新",
        "config": ai_config
    }

@app.delete("/process_mining/task/{task_id}")
async def delete_task(task_id: str):
    """删除任务及其相关文件"""
    if task_id not in task_status:
        raise HTTPException(status_code=404, detail="任务不存在")
    
    task = task_status[task_id]
    
    # 删除相关文件
    try:
        # 删除上传的JSON文件
        upload_path = os.path.join(UPLOAD_DIR, f"{task_id}.json")
        if os.path.exists(upload_path):
            os.remove(upload_path)
        
        # 删除生成的报告文件
        if task.output_file:
            output_path = os.path.join(OUTPUT_DIR, task.output_file)
            if os.path.exists(output_path):
                os.remove(output_path)
        
        # 从任务状态中删除
        del task_status[task_id]
        
        return {"message": "任务已删除"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"删除失败: {str(e)}")

@app.get("/process_mining/tasks")
async def list_tasks(
    status: Optional[str] = None,
    limit: int = 50,
    offset: int = 0
):
    """列出所有任务"""
    tasks = list(task_status.values())
    
    # 按状态过滤
    if status:
        tasks = [t for t in tasks if t.status == status]
    
    # 按创建时间排序（最新的在前）
    tasks.sort(key=lambda x: x.created_at, reverse=True)
    
    # 分页
    total = len(tasks)
    tasks = tasks[offset:offset + limit]
    
    return {
        "total": total,
        "limit": limit,
        "offset": offset,
        "tasks": tasks
    }

# 清理过期文件的定时任务
async def cleanup_old_files():
    """清理超过24小时的文件"""
    while True:
        await asyncio.sleep(3600)  # 每小时运行一次
        
        current_time = datetime.now()
        
        # 清理任务状态和文件
        for task_id, task in list(task_status.items()):
            if (current_time - task.created_at).total_seconds() > 86400:  # 24小时
                # 删除相关文件
                upload_path = os.path.join(UPLOAD_DIR, f"{task_id}.json")
                if os.path.exists(upload_path):
                    os.remove(upload_path)
                
                if task.output_file:
                    output_path = os.path.join(OUTPUT_DIR, task.output_file)
                    if os.path.exists(output_path):
                        os.remove(output_path)
                
                # 删除任务状态
                del task_status[task_id]

if __name__ == "__main__":
    import uvicorn
    
    # 运行服务器，使用正确的模块路径
    uvicorn.run(
        "api.api_server:app",  # 修正模块路径
        host="0.0.0.0",
        port=8004,
        reload=True,
        log_level="info"
    )