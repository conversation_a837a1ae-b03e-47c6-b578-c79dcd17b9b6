# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a Flow Mining Analysis Report Generator that creates professional Word documents from JSON process mining data. It's a bilingual (Chinese/English) project that uses AI models (primarily Qwen) to generate analysis content and supports both Python CLI and Java Spring Boot web interface implementations.

## Common Development Commands

### Python Environment
```bash
# Install dependencies
pip3 install -r requirements.txt

# Run the CLI tool
python src/main.py <json_file_path> [--output output_file] [--domain domain] [--log-level INFO]

# Run the web API server (primary interface)
python api/api_server.py

# Run tests
python -m pytest tests/

# Run specific test file
python -m pytest tests/test_ai_content.py
```

### Java Environment
```bash
# Navigate to Java project
cd java-flow-mining-analyzer

# Build and run (requires Java 8+, Maven 3.6+)
./start.sh

# Or manually:
mvn clean compile
mvn spring-boot:run

# Test
mvn test

# Package JAR for deployment
mvn clean package

# Run packaged JAR
java -jar target/flow-mining-analyzer-1.0.0.jar
```

### Docker
```bash
# Build and run with Docker (Python version)
docker build -t flow-mining-analyzer .

# Build specific versions
docker build -f Dockerfile.python -t flow-mining-analyzer-python .
docker build -f Dockerfile.java -t flow-mining-analyzer-java .

# Offline build
docker build -f Dockerfile.offline -t flow-mining-analyzer-offline .

# Run with docker-compose
docker-compose up -d

# Deploy (creates deployment package)
./deploy.sh
```

## Architecture Overview

### Python Implementation (Primary)
- **Entry Points**: `src/main.py` (CLI), `api/api_server.py` (FastAPI web server)
- **Data Processing**: `src/data/` - JSON loading, validation, and module extraction
- **AI Integration**: `src/ai/` - Client, content generation, title polishing using Qwen models
- **Document Generation**: `src/document/` - DOCX building, section creation, TOC generation
- **Configuration**: `config/` - AI settings, application settings
- **Utilities**: `src/utils/` - File operations, logging, text/time utilities

### Java Implementation (Alternative)
- **Spring Boot Application**: `java-flow-mining-analyzer/src/main/java/com/flowmining/`
- **Controller**: `ProcessMiningController` - REST API endpoints (basic implementation)
- **Models**: Task status, analysis request models in `model/` package
- **Configuration**: `application.yml`, `AppConfig.java`
- **Build**: Maven-based with Java 8 compatibility
- **Port**: Runs on port 8081 (configurable in application.yml)

### Key Components

1. **JSON Data Pipeline**:
   - `JsonLoader`: Loads and normalizes various JSON formats
   - `JsonValidator`: Validates data structure and content
   - `ModuleExtractor`: Separates regular and simple analysis modules

2. **AI Content Generation**:
   - `AIClient`: Handles API communication with Qwen models
   - `ContentGenerator`: Creates analysis content for different module types
   - `TitlePolisher`: Enhances section titles for professionalism

3. **Document Assembly**:
   - `DocxBuilder`: Core Word document creation and styling
   - `SectionFactory`: Orchestrates module processing and section creation
   - `TocGenerator`: Generates table of contents (requires manual Word refresh)

4. **Web Interface**:
   - FastAPI server with HTML interface for JSON upload and processing
   - Async task processing with status tracking
   - File management and download capabilities

## Configuration

### Environment Variables
- `DASHSCOPE_API_KEY`: Qwen API key (required for AI features)
- `OUTPUT_DIR`: Custom output directory
- `LOG_LEVEL`: Logging verbosity (DEBUG, INFO, WARNING, ERROR)
- `AI_API_KEY`: Java version AI API key (defaults to hardcoded key in application.yml)
- `AI_BASE_URL`: AI service base URL (Java version)

### Key Settings
- AI models and parameters configured in `config/ai_config.py`
- Output paths and application settings in `config/settings.py`
- Java configuration in `java-flow-mining-analyzer/src/main/resources/application.yml`

## Development Notes

### Data Flow
1. JSON input → validation → module extraction
2. Regular modules → AI analysis → formatted sections
3. Simple modules → summary table → optional AI enhancement
4. Document assembly → TOC generation → final Word output

### AI Integration
- Uses Alibaba's Qwen models via DashScope API
- Supports domain-specific analysis contexts
- Implements retry logic and rate limiting
- Configurable model parameters per task type

### Testing
- Python tests in `tests/` directory covering JSON processing, AI content, and document generation
- Java tests follow Spring Boot testing conventions
- Sample test data available in `test_data/` and `tests/test_data/`

### File Structure
- `uploads/`: Temporary JSON file storage
- `outputs/`: Generated Word documents
- `temp/`: Temporary processing files
- `logs/`: Application logs
- `wheels/` and `wheels_linux/`: Pre-built Python packages for offline installation
- `java-flow-mining-analyzer/`: Java Spring Boot implementation (basic REST API)
- `api/`: Python FastAPI web server implementation

## Implementation Status

### Python Implementation (Complete)
The Python implementation is fully functional with AI integration, document generation, and web interface.

### Java Implementation (Incomplete)
The Java implementation provides basic REST API endpoints but lacks:
- Service layer implementation (empty `service/` package)
- AI integration for content generation
- Document generation using Apache POI
- Actual file processing logic

The Java controller currently returns placeholder responses. To complete the Java implementation, implement:
1. `JsonLoaderService` for JSON processing
2. `AIClientService` for Qwen API integration
3. `DocumentBuilderService` for Word document generation
4. `ReportProcessorService` for orchestrating the full pipeline